from yunfu.db.graph.models import Node

from backend.graph import NebulaGraphMapper, Neo4jGraphMapper


class TestGraphMappersComparison:


    def test_get_node_by_eid(
        self,
        nebula_mapper: NebulaGraphMapper,
        neo4j_mapper: Neo4jGraphMapper,
        nebula_space: str,
        neo4j_space: str,
        nebula_eid: str,
        neo4j_eid: str,
    ):
        """测试get_node_by_eid方法的输出一致性"""
        nebula_result = nebula_mapper.get_node_by_eid(nebula_space, nebula_eid)
        neo4j_result = neo4j_mapper.get_node_by_eid(neo4j_space, neo4j_eid)

        # 如果都成功，比较返回类型
        assert isinstance(nebula_result, Node)
        assert isinstance(neo4j_result, Node)

        # 比较返回结果
        assert nebula_result.props["name"] == neo4j_result.props["name"]


    def test_get_max_relation_node_with_labels(
        self,
        nebula_mapper: NebulaGraphMapper,
        neo4j_mapper: Neo4jGraphMapper,
        nebula_space: str,
        neo4j_space: str,
        test_version: str,
    ):
        """测试get_max_relation_node_with_labels方法的输出一致性"""
        # 测试show_ontology=True的情况
        nebula_result_with_ontology = nebula_mapper.get_max_relation_node_with_labels(
            nebula_space, test_version, show_ontology=True
        )
        neo4j_result_with_ontology = neo4j_mapper.get_max_relation_node_with_labels(
            neo4j_space, test_version, show_ontology=True
        )

        # 验证返回类型一致
        assert isinstance(nebula_result_with_ontology, list)
        assert isinstance(neo4j_result_with_ontology, list)

        assert len(nebula_result_with_ontology) == len(neo4j_result_with_ontology)

        # 测试show_ontology=False的情况
        nebula_result_without_ontology = nebula_mapper.get_max_relation_node_with_labels(
            nebula_space, test_version, show_ontology=False
        )
        neo4j_result_without_ontology = neo4j_mapper.get_max_relation_node_with_labels(
            neo4j_space, test_version, show_ontology=False
        )

        # 验证返回类型一致
        assert isinstance(nebula_result_without_ontology, list)
        assert isinstance(neo4j_result_without_ontology, list)

        assert len(nebula_result_without_ontology) == len(neo4j_result_without_ontology)

    def test_get_single_node_with_labels(
        self,
        nebula_mapper: NebulaGraphMapper,
        neo4j_mapper: Neo4jGraphMapper,
        nebula_space: str,
        neo4j_space: str,
        test_version: str,
    ):
        """测试get_single_node_with_labels方法的输出一致性"""
        # 测试show_ontology=True的情况
        nebula_result_with_ontology = nebula_mapper.get_single_node_with_labels(
            nebula_space, test_version, show_ontology=True
        )
        neo4j_result_with_ontology = neo4j_mapper.get_single_node_with_labels(
            neo4j_space, test_version, show_ontology=True
        )

        # 验证返回类型一致
        assert isinstance(nebula_result_with_ontology, list)
        assert isinstance(neo4j_result_with_ontology, list)

        assert len(nebula_result_with_ontology) == len(neo4j_result_with_ontology)

        # 测试show_ontology=False的情况
        nebula_result_without_ontology = nebula_mapper.get_single_node_with_labels(
            nebula_space, test_version, show_ontology=False
        )
        neo4j_result_without_ontology = neo4j_mapper.get_single_node_with_labels(
            neo4j_space, test_version, show_ontology=False
        )

        # 验证返回类型一致
        assert isinstance(nebula_result_without_ontology, list)
        assert isinstance(neo4j_result_without_ontology, list)

        assert len(nebula_result_without_ontology) == len(neo4j_result_without_ontology)

    def test_count_kg(
        self,
        nebula_mapper: NebulaGraphMapper,
        neo4j_mapper: Neo4jGraphMapper,
        nebula_space: str,
        neo4j_space: str,
    ):
        """测试count_kg方法的输出一致性"""
        nebula_result = nebula_mapper.count_kg(nebula_space)
        neo4j_result = neo4j_mapper.count_kg(neo4j_space)

        # 验证返回结构一致
        assert isinstance(nebula_result, tuple)
        assert isinstance(neo4j_result, tuple)
        assert len(nebula_result) == len(neo4j_result) == 2

        # 验证第一个元素（results）的结构
        _, nebula_count_total = nebula_result
        _, neo4j_count_total = neo4j_result

        print(111111)
        print(nebula_result)
        print(neo4j_result)

        for key in ["entities", "relations", "properties"]:
            assert key in nebula_count_total
            assert key in neo4j_count_total
            assert nebula_count_total[key] == neo4j_count_total[key]


    def test_count_ontology(
        self,
        nebula_mapper: NebulaGraphMapper,
        neo4j_mapper: Neo4jGraphMapper,
        nebula_space: str,
        neo4j_space: str,
    ):
        """测试count_ontology方法的输出一致性"""
        nebula_result = nebula_mapper.count_ontology(nebula_space)
        neo4j_result = neo4j_mapper.count_ontology(neo4j_space)

        # 验证返回结构一致
        assert isinstance(nebula_result, tuple)
        assert isinstance(neo4j_result, tuple)
        assert len(nebula_result) == len(neo4j_result) == 2

        # 验证结构与count_kg类似
        _, nebula_count_total = nebula_result
        _, neo4j_count_total = neo4j_result

        print(111111)
        print(nebula_result)
        print(neo4j_result)

        for key in ["entities", "relations", "properties"]:
            assert key in nebula_count_total
            assert key in neo4j_count_total
            assert nebula_count_total[key] == neo4j_count_total[key]

    def test_count_kg_entities(
        self,
        nebula_mapper: NebulaGraphMapper,
        neo4j_mapper: Neo4jGraphMapper,
        nebula_space: str,
        neo4j_space: str,
    ):
        """测试count_kg_entities方法的输出一致性"""
        nebula_result = nebula_mapper.count_kg_entities(nebula_space)
        neo4j_result = neo4j_mapper.count_kg_entities(neo4j_space)

        # 验证返回类型一致
        assert isinstance(nebula_result, int)
        assert isinstance(neo4j_result, int)
        assert nebula_result >= 0
        assert neo4j_result >= 0
        assert nebula_result == neo4j_result

    def test_count_kg_properties(
        self,
        nebula_mapper: NebulaGraphMapper,
        neo4j_mapper: Neo4jGraphMapper,
        nebula_space: str,
        neo4j_space: str,
    ):
        """测试count_kg_properties方法的输出一致性"""
        nebula_result = nebula_mapper.count_kg_properties(nebula_space)
        neo4j_result = neo4j_mapper.count_kg_properties(neo4j_space)

        # 验证返回类型一致
        assert isinstance(nebula_result, list)
        assert isinstance(neo4j_result, list)

        assert len(nebula_result[0]) == len(neo4j_result[0])

    def test_count_kg_ontologies(
        self,
        nebula_mapper: NebulaGraphMapper,
        neo4j_mapper: Neo4jGraphMapper,
        nebula_space: str,
        neo4j_space: str,
    ):
        """测试count_kg_ontologies方法的输出一致性"""
        nebula_result = nebula_mapper.count_kg_ontologies(nebula_space)
        neo4j_result = neo4j_mapper.count_kg_ontologies(neo4j_space)

        # 验证返回类型一致
        assert isinstance(nebula_result, int)
        assert isinstance(neo4j_result, int)
        assert nebula_result >= 0
        assert neo4j_result >= 0

        assert nebula_result == neo4j_result

    def test_count_ontology_properties(
        self,
        nebula_mapper: NebulaGraphMapper,
        neo4j_mapper: Neo4jGraphMapper,
        nebula_space: str,
        neo4j_space: str,
    ):
        """测试count_ontology_properties方法的输出一致性"""
        nebula_result = nebula_mapper.count_ontology_properties(nebula_space)
        neo4j_result = neo4j_mapper.count_ontology_properties(neo4j_space)

        assert isinstance(nebula_result, list)
        assert isinstance(neo4j_result, list)

        assert len(nebula_result[0]) == len(neo4j_result[0])

    def test_get_relation_types(
        self,
        nebula_mapper: NebulaGraphMapper,
        neo4j_mapper: Neo4jGraphMapper,
        nebula_space: str,
        neo4j_space: str,
        nebula_eid: str,
        neo4j_eid: str,
    ):
        """测试get_relation_types方法的输出一致性"""
        # 测试不带eid参数的情况
        nebula_result_no_eid = nebula_mapper.get_relation_types(nebula_space)
        neo4j_result_no_eid = neo4j_mapper.get_relation_types(neo4j_space)

        # 验证返回类型一致
        assert isinstance(nebula_result_no_eid, list)
        assert isinstance(neo4j_result_no_eid, list)

        assert len(nebula_result_no_eid) == len(neo4j_result_no_eid)

        # 测试带eid参数的情况
        nebula_result_with_eid = nebula_mapper.get_relation_types(nebula_space, nebula_eid)
        neo4j_result_with_eid = neo4j_mapper.get_relation_types(neo4j_space, neo4j_eid)

        # 验证返回类型一致
        assert isinstance(nebula_result_with_eid, list)
        assert isinstance(neo4j_result_with_eid, list)

        assert len(nebula_result_with_eid) == len(neo4j_result_with_eid)

    def test_get_ontologies(
        self,
        nebula_mapper: NebulaGraphMapper,
        neo4j_mapper: Neo4jGraphMapper,
        nebula_space: str,
        neo4j_space: str,
    ):
        """测试get_ontologies方法的输出一致性"""
        nebula_result = nebula_mapper.get_ontologies(nebula_space)
        neo4j_result = neo4j_mapper.get_ontologies(neo4j_space)

        # 验证返回类型一致
        assert isinstance(nebula_result, list)
        assert isinstance(neo4j_result, list)

        assert len(nebula_result) == len(neo4j_result)
